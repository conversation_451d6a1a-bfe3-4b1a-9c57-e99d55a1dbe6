import requests
import json

# API 基础 URL
BASE_URL = "http://localhost:8000"

def test_health_check():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

def test_diagnose_engine_issue():
    """测试发动机故障诊断"""
    print("=== 测试发动机故障诊断 ===")
    data = {
        "symptom": "发动机启动困难，怠速不稳",
        "car_model": "丰田凯美瑞",
        "mileage": 120000
    }
    
    try:
        response = requests.post(f"{BASE_URL}/diagnose", json=data)
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print(f"响应数据: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

def test_diagnose_brake_issue():
    """测试刹车故障诊断"""
    print("=== 测试刹车故障诊断 ===")
    data = {
        "symptom": "刹车异响，制动距离变长",
        "car_model": "本田雅阁",
        "mileage": 80000
    }
    
    try:
        response = requests.post(f"{BASE_URL}/diagnose", json=data)
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print(f"响应数据: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

def test_diagnose_transmission_issue():
    """测试变速箱故障诊断"""
    print("=== 测试变速箱故障诊断 ===")
    data = {
        "symptom": "换挡顿挫，变速箱异响",
        "car_model": "大众帕萨特",
        "mileage": 95000
    }
    
    try:
        response = requests.post(f"{BASE_URL}/diagnose", json=data)
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print(f"响应数据: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

def test_invalid_request():
    """测试无效请求"""
    print("=== 测试无效请求 ===")
    data = {
        "symptom": "",  # 空症状
        "car_model": "测试车型",
        "mileage": -1000  # 负数里程
    }
    
    try:
        response = requests.post(f"{BASE_URL}/diagnose", json=data)
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        print(f"错误响应: {json.dumps(response.json(), ensure_ascii=False, indent=2)}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        print()

def main():
    """运行所有测试"""
    print("开始测试 AI 汽车故障诊断系统 API")
    print("=" * 50)
    
    # 测试健康检查
    test_health_check()
    
    # 测试各种故障诊断场景
    test_diagnose_engine_issue()
    test_diagnose_brake_issue()
    test_diagnose_transmission_issue()
    
    # 测试错误处理
    test_invalid_request()
    
    print("测试完成！")

if __name__ == "__main__":
    main()
