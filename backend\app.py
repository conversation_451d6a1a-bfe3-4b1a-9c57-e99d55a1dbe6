from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn

app = FastAPI(title="AI汽车故障诊断系统", version="1.0.0")

# 请求模型
class DiagnoseRequest(BaseModel):
    symptom: str
    car_model: str
    mileage: int

# 响应模型
class ComponentRecommendation(BaseModel):
    component: str
    probability: float
    description: str

class MaintenanceAdvice(BaseModel):
    priority: str  # "高", "中", "低"
    action: str
    estimated_cost: str
    urgency: str

class DiagnoseResponse(BaseModel):
    recommended_components: List[ComponentRecommendation]
    maintenance_advice: List[MaintenanceAdvice]
    overall_assessment: str

# 故障诊断逻辑
def diagnose_car_issue(symptom: str, car_model: str, mileage: int) -> DiagnoseResponse:
    """
    基于症状、车型和里程数进行故障诊断
    """
    symptom_lower = symptom.lower()
    
    # 基础故障诊断规则
    components = []
    advice = []
    
    # 发动机相关症状
    if any(keyword in symptom_lower for keyword in ["发动机", "启动困难", "怠速不稳", "动力不足", "异响"]):
        if mileage > 100000:
            components.append(ComponentRecommendation(
                component="火花塞",
                probability=0.8,
                description="高里程车辆火花塞磨损严重，可能导致启动困难和动力不足"
            ))
            components.append(ComponentRecommendation(
                component="空气滤清器",
                probability=0.7,
                description="空气滤清器堵塞会影响发动机进气，导致动力下降"
            ))
        else:
            components.append(ComponentRecommendation(
                component="燃油系统",
                probability=0.6,
                description="燃油泵或喷油嘴可能存在问题"
            ))
        
        advice.append(MaintenanceAdvice(
            priority="高",
            action="立即检查发动机系统，进行全面诊断",
            estimated_cost="500-2000元",
            urgency="建议3天内处理"
        ))
    
    # 刹车相关症状
    elif any(keyword in symptom_lower for keyword in ["刹车", "制动", "刹车片", "异响", "制动距离"]):
        components.append(ComponentRecommendation(
            component="刹车片",
            probability=0.9,
            description="刹车片磨损是最常见的制动系统问题"
        ))
        components.append(ComponentRecommendation(
            component="刹车盘",
            probability=0.6,
            description="刹车盘可能存在磨损或变形"
        ))
        
        advice.append(MaintenanceAdvice(
            priority="高",
            action="立即检查制动系统，确保行车安全",
            estimated_cost="300-1500元",
            urgency="立即处理"
        ))
    
    # 变速箱相关症状
    elif any(keyword in symptom_lower for keyword in ["换挡", "变速箱", "顿挫", "打滑"]):
        components.append(ComponentRecommendation(
            component="变速箱油",
            probability=0.8,
            description="变速箱油老化或不足可能导致换挡问题"
        ))
        components.append(ComponentRecommendation(
            component="离合器",
            probability=0.7,
            description="手动挡车辆离合器可能需要调整或更换"
        ))
        
        advice.append(MaintenanceAdvice(
            priority="中",
            action="检查变速箱系统，更换变速箱油",
            estimated_cost="800-3000元",
            urgency="建议1周内处理"
        ))
    
    # 轮胎相关症状
    elif any(keyword in symptom_lower for keyword in ["轮胎", "胎压", "磨损", "跑偏"]):
        components.append(ComponentRecommendation(
            component="轮胎",
            probability=0.9,
            description="轮胎磨损不均或胎压异常"
        ))
        components.append(ComponentRecommendation(
            component="四轮定位",
            probability=0.6,
            description="车辆可能需要进行四轮定位调整"
        ))
        
        advice.append(MaintenanceAdvice(
            priority="中",
            action="检查轮胎状况和胎压，必要时进行四轮定位",
            estimated_cost="200-1000元",
            urgency="建议2周内处理"
        ))
    
    # 电气系统相关症状
    elif any(keyword in symptom_lower for keyword in ["电瓶", "电池", "启动", "灯光", "电气"]):
        components.append(ComponentRecommendation(
            component="蓄电池",
            probability=0.8,
            description="蓄电池老化或电量不足"
        ))
        components.append(ComponentRecommendation(
            component="发电机",
            probability=0.5,
            description="发电机可能存在充电问题"
        ))
        
        advice.append(MaintenanceAdvice(
            priority="中",
            action="检查电气系统，测试蓄电池和发电机",
            estimated_cost="300-1200元",
            urgency="建议1周内处理"
        ))
    
    # 默认建议
    else:
        components.append(ComponentRecommendation(
            component="常规保养项目",
            probability=0.5,
            description="建议进行全面检查以确定具体问题"
        ))
        
        advice.append(MaintenanceAdvice(
            priority="低",
            action="进行全面车辆检查，确定具体故障原因",
            estimated_cost="200-500元",
            urgency="建议1个月内处理"
        ))
    
    # 根据里程数添加额外建议
    if mileage > 150000:
        advice.append(MaintenanceAdvice(
            priority="中",
            action="高里程车辆建议进行大保养，更换主要易损件",
            estimated_cost="2000-5000元",
            urgency="建议近期安排"
        ))
    elif mileage > 80000:
        advice.append(MaintenanceAdvice(
            priority="低",
            action="中等里程车辆建议定期保养，预防性维护",
            estimated_cost="500-1500元",
            urgency="按保养周期进行"
        ))
    
    # 生成总体评估
    if any(adv.priority == "高" for adv in advice):
        overall = f"根据症状'{symptom}'和车辆里程{mileage}公里的分析，发现可能存在较严重的问题，建议尽快处理。"
    elif any(adv.priority == "中" for adv in advice):
        overall = f"车辆'{car_model}'出现的症状需要关注，建议及时进行相关检查和维护。"
    else:
        overall = f"车辆状况相对良好，建议按正常保养周期进行维护即可。"
    
    return DiagnoseResponse(
        recommended_components=components,
        maintenance_advice=advice,
        overall_assessment=overall
    )

@app.get("/")
async def root():
    return {"message": "AI汽车故障诊断系统 API"}

@app.post("/diagnose", response_model=DiagnoseResponse)
async def diagnose_car(request: DiagnoseRequest):
    """
    汽车故障诊断接口
    
    接收参数：
    - symptom: 故障症状描述
    - car_model: 车型
    - mileage: 里程数
    
    返回：
    - recommended_components: 推荐检查的故障组件
    - maintenance_advice: 维修建议
    - overall_assessment: 总体评估
    """
    try:
        # 参数验证
        if not request.symptom.strip():
            raise HTTPException(status_code=400, detail="症状描述不能为空")
        
        if not request.car_model.strip():
            raise HTTPException(status_code=400, detail="车型不能为空")
        
        if request.mileage < 0:
            raise HTTPException(status_code=400, detail="里程数不能为负数")
        
        if request.mileage > 1000000:
            raise HTTPException(status_code=400, detail="里程数超出合理范围")
        
        # 执行诊断
        result = diagnose_car_issue(request.symptom, request.car_model, request.mileage)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"诊断过程中发生错误: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "AI汽车故障诊断系统"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
