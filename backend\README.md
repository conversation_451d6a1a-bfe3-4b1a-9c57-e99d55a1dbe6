# AI汽车故障诊断系统 Backend

基于 FastAPI 构建的汽车故障诊断后端服务。

## 功能特性

- 🚗 智能故障诊断：根据症状、车型和里程数提供诊断建议
- 🔧 组件推荐：推荐可能故障的汽车组件及概率
- 💡 维修建议：提供详细的维修建议和预估费用
- 📊 优先级评估：根据问题严重程度提供处理优先级

## 安装和运行

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 启动服务

```bash
python app.py
```

服务将在 `http://localhost:8000` 启动。

### 3. 查看 API 文档

启动服务后，访问以下地址查看自动生成的 API 文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## API 接口

### POST /diagnose

汽车故障诊断接口

**请求参数：**

```json
{
  "symptom": "发动机启动困难",
  "car_model": "丰田凯美瑞",
  "mileage": 120000
}
```

**响应示例：**

```json
{
  "recommended_components": [
    {
      "component": "火花塞",
      "probability": 0.8,
      "description": "高里程车辆火花塞磨损严重，可能导致启动困难和动力不足"
    },
    {
      "component": "空气滤清器",
      "probability": 0.7,
      "description": "空气滤清器堵塞会影响发动机进气，导致动力下降"
    }
  ],
  "maintenance_advice": [
    {
      "priority": "高",
      "action": "立即检查发动机系统，进行全面诊断",
      "estimated_cost": "500-2000元",
      "urgency": "建议3天内处理"
    }
  ],
  "overall_assessment": "根据症状'发动机启动困难'和车辆里程120000公里的分析，发现可能存在较严重的问题，建议尽快处理。"
}
```

### GET /health

健康检查接口

**响应：**

```json
{
  "status": "healthy",
  "service": "AI汽车故障诊断系统"
}
```

## 测试示例

使用 curl 测试接口：

```bash
curl -X POST "http://localhost:8000/diagnose" \
     -H "Content-Type: application/json" \
     -d '{
       "symptom": "刹车异响",
       "car_model": "本田雅阁",
       "mileage": 80000
     }'
```

## 支持的故障类型

系统目前支持以下类型的故障诊断：

- **发动机问题**：启动困难、怠速不稳、动力不足、异响等
- **制动系统**：刹车异响、制动距离长、刹车片磨损等
- **变速箱问题**：换挡顿挫、打滑、异响等
- **轮胎问题**：磨损不均、胎压异常、跑偏等
- **电气系统**：电瓶问题、启动困难、灯光故障等

## 技术栈

- **FastAPI**: 现代、快速的 Web 框架
- **Pydantic**: 数据验证和序列化
- **Uvicorn**: ASGI 服务器

## 开发计划

- [ ] 集成机器学习模型提高诊断准确性
- [ ] 添加更多车型和故障类型的支持
- [ ] 集成汽车维修数据库
- [ ] 添加用户认证和历史记录功能
- [ ] 支持图片上传进行视觉诊断
